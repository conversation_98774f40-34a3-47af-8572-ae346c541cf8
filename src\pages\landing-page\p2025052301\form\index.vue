<script setup lang="ts">
import NavBar from '@/components/NavBar/index.vue'
import PlateNumber from './components/PlateNumber/index.vue'
import RegionPicker from './components/RegionPicker/index.vue'
// import CarStatus from './components/CarStatus/index.vue'
import IdentityCard from './components/IdentityCard/index.vue'
import PageFooter from '@/components/PageFooter/index.vue'
import BackTop from '@/components/BackTop/index.vue'

import { useForm } from '@/pages/composables/useForm'
import { TrackClickButton, useTrack } from './hooks/track'
import { ref } from 'vue'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { isLogin } from '@/utils/auth'
import { useFormTracking } from '@/composables/useFormTracking'
// import { useDialog } from '@revfanc/use'

defineOptions({
  beforeRouteEnter(to, from, next) {
    if (isLogin()) {
      next()
    }
    else {
      next({
        path: from.path || to.path.replace('/form', ''),
        query: {
          ...from.query,
        },
      })
    }
  },
})

// const DialogLoginUpdate = defineAsyncComponent(() => import('@/pages/landing-page/p2025040901/components/DialogLoginUpdate/index.vue'))
// const DialogForm = defineAsyncComponent(() => import('./components/DialogForm/index.vue'))
// const DialogLeave = defineAsyncComponent(() => import('./components/DialogLeave/index.vue'))

// const isBack = ref(false)

const keyboardFocus = ref(false)

const pageBackInterceptor = usePageBackInterceptor()
const unionLogin = useUnionLogin()

// const dialog = useDialog()
const {
  info,
  region,
  identityCards,
  plateNumberRef,
  plateNumber,
  // carStatus,
  readProtocol,
  agreementCheckTime,
  isUnionLogin,
  showProtocol,
  // getInfo,
  goPicture,
  applyLoan,
} = useForm({ isDistribution: true, isInstitutionalAgreementVisible: true })

const showIdentityCard = computed(() => plateNumber.value?.length >= 7)

const TRACK_PAGE = 'userInfoPage'

const formFields = computed(() => ({
  region: region.value,
  plateNumber: plateNumber.value,
  identityCards: identityCards.value,
}))

const { trackInputStart, trackInputComplete } = useFormTracking({
  page: TRACK_PAGE,
  formFields,
})

const track = useTrack()

watch(() => keyboardFocus.value, (val) => {
  if (val) {
    scrollPageToMiddle('plateNumber')
  }
  else if (!val && plateNumber.value?.length >= 7) {
    scrollPageToMiddle('identityCard')
  }
})

function trackClick(button) {
  track.click({
    page: TRACK_PAGE,
    button,
    extendJson: {
      pageType: 4,
    },
  })
}

function handleBack() {
  trackClick(TrackClickButton.BACK)

  // if (!info.value?.licensePlateNumber && plateNumber.value?.length < 7 && !isBack.value) {
  //   showDialogLeave()
  //   isBack.value = true
  //   return false
  // }
  // if ((!info.value?.idCardFrontUrl || !info.value?.idCardBackUrl) && (!identityCards.value?.front || !identityCards.value?.back) && !isBack.value) {
  //   showDialogForm('identityCard')
  //   isBack.value = true
  //   return false
  // }

  return true
}

// function showDialogLeave() {
//   return dialog.open({
//     render(context) {
//       return h(DialogLeave, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'confirm') {
//       getInfo()
//     }
//   })
// }

// function showDialogForm(itemType) {
//   return dialog.open({
//     render(context) {
//       return h(DialogForm, {
//         itemType,
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'confirm') {
//       getInfo()
//     }
//   })
// }

function uploadTwice() {
  if (identityCards.value.front && identityCards.value.back) {
    trackClick(TrackClickButton.FINISH_ID)
  }

  trackInputComplete('identityCards')
}

async function scrollPageToMiddle(type: string) {
  const target: HTMLElement = document.querySelector('#form')
  if (!target)
    return
  const top = target.offsetTop as number
  window.scrollTo({
    top: type === 'plateNumber' ? top - 50 : top,
    behavior: 'smooth',
  })
}

// function invokeKeyboardFocus() {
//   plateNumberRef.value?.handleShowKeyboard()
// }

function navBackIconClick() {
  const shouldBack = handleBack()

  if (shouldBack) {
    window.history.back()
  }
}

// function showDialogLoginUpdate() {
//   return dialog.open({
//     render(context) {
//       return h(DialogLoginUpdate, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'success') {
//       window.location.reload()
//     }
//     return Promise.reject(res)
//   })
// }

function toggleProtocol() {
  readProtocol.value = !readProtocol.value
  if (readProtocol.value) {
    agreementCheckTime.value = Date.now()
  }
}

onMounted(() => {
  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
      extendJson: {
        pageType: 4,
      },
    })
  })

  track.stay({
    page: TRACK_PAGE,
    extendJson: {
      pageType: 4,
    },
  })

  // 属于联登录需要设置返回到指定页面
  if (unionLogin.isUnionLogin.value) {
    pageBackInterceptor.setBackHandler()
  }

  pageBackInterceptor.add(handleBack)
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})
</script>

<template>
  <div class="home-page">
    <NavBar :background-custom-style="{ backgroundColor: '#fff' }">
      <template #left>
        <img class="icon-back" src="@/pages/landing-page/p2025032401/images/icon_back.png" @click="navBackIconClick()">
      </template>
    </NavBar>

    <div class="header" />

    <form id="form" class="form" @submit.prevent>
      <div class="box-content">
        <div class="title">
          手机号
        </div>
        <div class="row">
          <div class="label">
            {{ info?.mobileNo }}
          </div>
          <!-- <div class="edit">
            修改
          </div> -->
        </div>
      </div>

      <div class="box-content">
        <div class="title">
          归属地
        </div>
        <!-- 地区选择 -->
        <RegionPicker v-model="region" @invoke="trackClick(TrackClickButton.CITY), trackInputStart('region')" @done="trackInputComplete('region')">
          <template #default="{ handleSelect }">
            <div class="row" @click="handleSelect">
              <div class="label">
                {{ region.cityName || '*' }}
              </div>
              <div class="arrow" />
            </div>
          </template>
        </RegionPicker>
      </div>

      <div class="box-content">
        <div class="title">
          车牌号码
        </div>
        <!-- 车牌输入区域 -->
        <div class="row">
          <PlateNumber
            id="plate-number" ref="plateNumberRef" v-model="plateNumber" class="plate"
            @invoke="trackClick(TrackClickButton.CAR)" @done="trackClick(TrackClickButton.FINISH)"
            @focus="(keyboardFocus = true), trackInputStart('plateNumber')" @blur="(keyboardFocus = false), trackInputComplete('plateNumber')"
          />
        </div>
      </div>

      <!-- 车辆状态 -->
      <!-- <div class="box-content">
        <div class="title">
          车辆状态
        </div>
        <div class="row">
          <CarStatus v-model="carStatus" @change="trackClick(TrackClickButton.CAR_STATUS)" />
        </div>
      </div> -->

      <!-- 上传身份证 -->
      <IdentityCard
        v-show="showIdentityCard"
        class="identity-card" :info="info" :identity-cards="identityCards"
        @picture="goPicture($event).finally(() => { uploadTwice() }), trackClick(TrackClickButton.ID), trackInputStart('identityCards')"
      />
      <div class="footer">
        <button class="submit-btn bounce" @click="applyLoan(), trackClick(TrackClickButton.GET_QUOTA)">
          <img class="hand" src="@/assets/images/home/<USER>">
        </button>

        <!-- 协议 -->
        <div class="protocol" @click="toggleProtocol">
          <IconCheck class="icon" :style="{ color: readProtocol ? '#2F79FF' : '#999999' }" />
          <span class="text">我已阅读并同意<span
            class="blue"
            @click.stop="showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')"
          >《授权相关协议》
          </span>
          </span>
        </div>
      </div>
    </form>

    <!-- 返回顶部 -->
    <BackTop>
      <div class="back-top" />
    </BackTop>

    <!-- 页脚 -->
    <PageFooter />

    <div class="footer-placeholder" />
  </div>
</template>

<style scoped lang="less">
.home-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #ecf5fc;

  .icon-back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
  }

  .header {
    width: 375px;
    height: 180px;
    background-image: url('@/pages/landing-page/p2025040901/images/banner3.png');
    background-size: 375px 180px;
    background-repeat: no-repeat;
    margin-bottom: -87px;
  }

  .box-content {
    width: 345px;
    background: #ffffff;
    box-shadow: 0px 0px 3px 0px rgba(0, 49, 118, 0.12);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    margin: 10px auto 0;

    .title {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      margin: 15px 15px 0;
      position: relative;

      &::before {
        content: '';
        width: 2px;
        height: 12px;
        background: #1677ff;
        border-radius: 1px;
        position: absolute;
        left: -5px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .row {
      margin: 15px 10px 0;
      display: flex;
      align-items: center;

      &:last-child {
        margin-bottom: 15px;
      }

      .label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }

      .edit {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1677ff;
        line-height: 20px;
        margin-left: auto;
      }

      .arrow {
        width: 5px;
        height: 10px;
        margin-left: auto;
        background-image: url('@/pages/landing-page/p2025040901/images/icon_back.png');
        background-size: 100% 100%;
        transform: rotate(180deg);
      }

      .plate {
        margin: 0 5px;
      }
    }
  }

  .form {
    display: flex;
    flex-direction: column;

    .identity-card {
      margin: -14px auto 0;
    }

    .footer {
      width: 375px;
      background: #ffffff;
      box-shadow: 0px -1px 5px 0px rgba(47, 121, 255, 0.1);
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 19;
      padding-bottom: env(safe-area-inset-bottom);
      padding-bottom: constant(safe-area-inset-bottom);

      .submit-btn {
        display: block;
        width: 331px;
        height: 61px;
        margin: 10px auto;
        position: relative;
        background-image: url('@/pages/landing-page/p2025040901/images/btn2.png');
        background-size: 100% 100%;

        .hand {
          width: 50px;
          height: 60px;
          position: absolute;
          right: -11px;
          bottom: -30px;
        }
      }
    }
  }

  .footer-placeholder {
    height: calc(81px + env(safe-area-inset-bottom));
    height: calc(81px + constant(safe-area-inset-bottom));
  }

  .back-top {
    width: 87px;
    height: 35px;
    margin: 7px auto 0;
    background-image: url('@/assets/images/home/<USER>');
    background-size: 100% 100%;
  }

  .protocol {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    margin: -5px auto 10px;

    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;

      &.active {
        color: #1677ff;
      }
    }

    .text {
      vertical-align: middle;

      .blue {
        color: #1677ff;
      }
    }
  }
}
</style>
