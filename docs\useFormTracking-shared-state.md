# useFormTracking 共享状态使用指南

## 问题描述

当页面和弹窗同时需要使用 `useFormTracking` 时，由于不是共用同一个环境，导致数据无法共享。比如页面可能填写了姓名、车牌、车辆状态等，弹窗根据车辆状态判断是否需要填写身份证信息。

## 解决方案

通过全局状态管理来实现页面和弹窗间的状态共享。

## 使用方法

### 1. 页面中使用全局状态

```typescript
// 页面组件中
import { useFormTracking } from '@/composables/useFormTracking'

const formFields = computed(() => ({
  region: region.value,
  plateNumber: plateNumber.value,
  carStatus: carStatus.value,
  realName: realName.value,
}))

// 启用全局状态共享
const { trackInputStart, trackInputComplete } = useFormTracking({
  page: 'userInfoPage',
  formFields,
  useGlobalState: true, // 关键配置：启用全局状态
})
```

### 2. 弹窗中使用全局状态

```typescript
// 弹窗组件中
import { useFormTracking } from '@/composables/useFormTracking'

const formFields = computed(() => ({
  identityCards: props.identityCards,
}))

// 同样启用全局状态共享
const { trackInputStart, trackInputComplete } = useFormTracking({
  page: 'identityCardPopup',
  formFields,
  useGlobalState: true, // 关键配置：启用全局状态
})
```

### 3. 访问全局状态

```typescript
import { getGlobalFormTrackingState } from '@/composables/useFormTracking'

// 获取全局状态
const globalState = getGlobalFormTrackingState()

// 检查某个字段是否已经开始输入
if (globalState.value.carStatusStarted) {
  // 根据车辆状态判断是否需要填写身份证
  if (carStatus.value === 2) {
    // 显示身份证输入
  }
}
```

### 4. 重置全局状态

```typescript
import { resetGlobalFormTrackingState } from '@/composables/useFormTracking'

// 在适当的时机重置状态（比如用户重新开始填写表单）
resetGlobalFormTrackingState()
```

## 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `useGlobalState` | `boolean` | `false` | 是否使用全局状态，启用后页面和弹窗可以共享追踪状态 |

## 最佳实践

1. **统一配置**：在需要共享状态的页面和弹窗中都设置 `useGlobalState: true`
2. **状态重置**：在用户开始新的表单流程时调用 `resetGlobalFormTrackingState()`
3. **状态检查**：使用 `getGlobalFormTrackingState()` 来检查其他组件的输入状态
4. **避免混用**：在同一个表单流程中，要么全部使用全局状态，要么全部使用本地状态，避免混用

## 示例场景

### 场景：根据车辆状态决定是否显示身份证弹窗

```typescript
// 页面组件
const { trackInputStart, trackInputComplete } = useFormTracking({
  page: 'userInfoPage',
  formFields: computed(() => ({
    plateNumber: plateNumber.value,
    carStatus: carStatus.value,
  })),
  useGlobalState: true,
})

// 监听车辆状态变化
watch(carStatus, (newStatus) => {
  const globalState = getGlobalFormTrackingState()
  
  // 如果车辆状态为"有贷款"且用户已经开始输入车辆状态
  if (newStatus === 2 && globalState.value.carStatusStarted) {
    // 显示身份证弹窗
    showIdentityCardDialog()
  }
})

// 身份证弹窗组件
const { trackInputStart, trackInputComplete } = useFormTracking({
  page: 'identityCardPopup',
  formFields: computed(() => ({
    identityCards: identityCards.value,
  })),
  useGlobalState: true, // 共享状态
})
```

这样，弹窗就能知道页面中用户已经填写了哪些字段，从而做出相应的业务逻辑判断。
