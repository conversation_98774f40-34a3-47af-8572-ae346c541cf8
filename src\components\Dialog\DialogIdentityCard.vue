<script setup lang="ts">
import IdentityCard from '@/components/IdentityCard/index.vue'
import DialogIdentityCardNotice from './DialogIdentityCardNotice.vue'
import { useDialog } from '@revfanc/use'
import { useTrack } from '@/composables/useTrack'
import { useFormTracking } from '@/composables/useFormTracking'
import { showToast } from 'vant'

const props = defineProps({
  info: {
    type: Object,
    required: true,
  },
  identityCards: {
    type: Object,
    required: true,
  },
  goPicture: {
    type: Function,
    required: true,
  },
  updateIdentityCard: {
    type: Function,
    required: true,
  },
})

const emit = defineEmits(['action'])

// 1：关闭；2：身份证点击；3：身份证开始输入；4：身份证输入完成；5：立即提交
enum TrackClickButton {
  CLOSE = 1,
  UPLOAD_ID = 2,
  START_ID = 3,
  FINISH_ID = 4,
  SUBMIT_ID = 5,
}

const TRACK_PAGE = 'identityCardPopup'

const formFields = computed(() => ({
  identityCards: {
    front: props.identityCards.value.front || props.info.value.idCardFrontUrl,
    back: props.identityCards.value.back || props.info.value.idCardBackUrl,
  },
}))

const { trackInputStart, trackInputComplete } = useFormTracking({
  page: TRACK_PAGE,
  formFields,
})

const dialog = useDialog()

function showNoticeDialog() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogIdentityCardNotice, {
        onAction: context.callback,
      })
    },
  })
}

const track = useTrack({
  page: TRACK_PAGE,
  autoShow: true,
  autoStay: true,
})

function uploadTwice() {
  if (props.identityCards.value.front && props.identityCards.value.back) {
    track.click({ button: TrackClickButton.FINISH_ID })
  }
  else if (props.identityCards.value.front || props.identityCards.value.back) {
    track.click({ button: TrackClickButton.START_ID })
  }

  trackInputComplete('identityCards')
}

function onSubmit() {
  if (!props.identityCards.value.front || !props.identityCards.value.back) {
    showToast('请上传身份证')
    return
  }

  emit('action', { action: 'confirm' })
}
</script>

<template>
  <div class="dialog-form">
    <img
      class="close" src="@/assets/images/home/<USER>"
      @click="$emit('action', { action: 'close' }), track.click({ button: TrackClickButton.CLOSE })"
    >
    <div class="title">
      还差1步 查看额度
    </div>
    <form class="form" @submit.prevent>
      <!-- 上传身份证 -->
      <IdentityCard
        class="identity-card"
        :info="info"
        :identity-cards="identityCards"
        @notice="showNoticeDialog"
        @picture="goPicture($event).finally(() => { uploadTwice() }), track.click({ button: TrackClickButton.UPLOAD_ID }), trackInputStart('identityCards')"
      />
      <button
        class="submit-btn bounce"
        @click="onSubmit(), track.click({ button: TrackClickButton.SUBMIT_ID })"
      >
        立即提额
        <img class="hand" src="@/assets/images/home/<USER>">
      </button>
    </form>
  </div>
</template>

<style lang='less' scoped>
.dialog-form {
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s;
  width: 375px;
  height: auto;
  background: #ffffff;
  border-radius: 24px 24px 0px 0px;

  .close {
    width: 21px;
    height: 21px;
    position: absolute;
    right: 12px;
    top: 14px;
    z-index: 3;
  }

  .title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 25px;
    margin: 12px auto 0;
  }
  .form {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;

    .identity-card {
      margin: 12px 12px 0;
    }

    .submit-btn {
      display: block;
      width: 335px;
      height: 53px;
      background: #1677ff;
      box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
      border-radius: 9999px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #ffffff;
      line-height: 25px;
      margin: 15px auto 34px;
      position: relative;

      .hand {
        width: 50px;
        height: 60px;
        position: absolute;
        right: -11px;
        bottom: -30px;
      }
    }

    .desc {
      width: 157px;
      height: 16px;
      display: block;
      margin: 20px auto 0;
    }
  }
}
</style>
