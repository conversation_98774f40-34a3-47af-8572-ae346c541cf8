<script setup lang="ts">
import CarStepLayout from '@/PageComponents/CarBeforeApplyLoanStep/components/CarStepLayout.vue'
import PlateNumber from '../form/components/PlateNumber/index.vue'
import RegionPicker from '../form/components/RegionPicker/index.vue'
import CarStatus from '../form/components/CarStatus/index.vue'
// import IdentityCard from '../form/components/IdentityCard/index.vue'
import IconCheckWhite from '@/components/IconCheckWhite/index.vue'
import MyNavBar from '@/PageComponents/CarBeforeApplyLoanStep/components/MyNavBar.vue'
import stepBg from '@/assets/images/p2025072201/bg.png'
import StepLine from './components/StepLine.vue'
import type { Step } from './components/StepLine.vue'

import { isIOS } from '@/utils/device'
import { useForm } from '@/pages/composables/useForm'
import { TrackClickButton, useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { isLogin } from '@/utils/auth'
import { useDialog } from '@revfanc/use'
import dialogMount from '@/utils/dialog-mount'
import { useCarBeforeApplyLoanStep } from '@/composables/useCarBeforeApplyLoanStep'

defineOptions({
  beforeRouteEnter(to, from, next) {
    if (isLogin()) {
      next()
    }
    else {
      next({
        path: from.path || to.path.replace('/form', ''),
        query: {
          ...from.query,
        },
      })
    }
  },
})

const status = useSessionStorage('CARSTEP_STATUS', {
  isReverse: null,
})
// const _disableProtocol = computed(() => status.value.isReverse === 1)

const DialogAmountIntercept = dialogMount(() => import('@/pages/landing-page/components/Dialog/PageInterceptIdentityV2/index.vue'))
// const DialogLoginUpdate = defineAsyncComponent(() => import('@/pages/landing-page/p2025040901/components/DialogLoginUpdate/index.vue'))
// const DialogForm = defineAsyncComponent(() => import('../form/components/DialogForm/index.vue'))

const isBack = ref(false)

const keyboardFocus = ref(false)

const pageBackInterceptor = usePageBackInterceptor()
const unionLogin = useUnionLogin()

const dialog = useDialog()

const { goBack } = useCarBeforeApplyLoanStep({ autoTrack: false })

const {
  info,
  region,
  realNameRef,
  realName,
  plateNumberRef,
  plateNumber,
  carStatus,
  readProtocol,
  agreementCheckTime,
  isUnionLogin,
  showProtocol,
  getInfo,
  // goPicture,
  applyLoan,
} = useForm({
  isClue: true,
  disableProtocol: status.value.isReverse !== 1,
  disableCheckRealName: false,
  disableCheckRegion: true,
  isCheckIdentityCardByDialog: true,
  isInstitutionalAgreementVisible: true,
  footer: {
    record: 'CQ',
    footerTips: 'no',
  },
})

// const showIdentityCard = computed(() => plateNumber.value?.length >= 7)
const showKeyboard = computed(() => plateNumberRef?.value?.showKeyboard)
const realNameFocus = ref<boolean>(false)

const TRACK_PAGE = status.value.isReverse === 1 ? 'userInfoReversePage' : 'userInfoPage'

const track = useTrack()

// --- StepLine 组件逻辑 ---

// 1. 确定组件挂载时的一次性动画类型。
const animationType = computed<'full' | 'partial' | 'special_case' | 'none'>(() => {
  if (realName.value && carStatus.value && plateNumber.value?.length >= 7) {
    return 'full'
  }
  if (!realName.value && carStatus.value && plateNumber.value?.length >= 7) {
    return 'special_case'
  }
  if (realName.value && carStatus.value) {
    return 'partial'
  }
  return 'none'
})

// 2. 根据零散的规则计算步骤的实时状态。
const stepsConfig = computed<Step[]>(() => {
  // 规则 2 & 3: 节点完成状态。
  const node1Completed = !!realName.value
  const node3Completed = plateNumber.value?.length >= 7

  // 规则 1: carStatus 的特殊情况。
  const node2Completed = !!carStatus.value
  let line1IsBlue = node1Completed // 默认的 line1 状态。
  if (!realName.value && carStatus.value) {
    line1IsBlue = true
  }

  // - Line 2 的标签 *仅* 由 realName 是否有值来控制。
  const line2Text = realName.value ? '仅差一步' : ''

  // 线的完成状态。
  const line2Completed = node2Completed

  return [
    {
      nodeStatus: node1Completed ? 'completed' : 'pending',
      lineStatus: line1IsBlue ? 'completed' : 'pending',
      node: { animationDuration: 200 },
      line: {
        animationDuration: 500,
      },
    },
    {
      nodeStatus: node2Completed ? 'completed' : 'pending',
      lineStatus: line2Completed ? 'completed' : 'pending',
      node: { animationDuration: 200 },
      line: {
        text: line2Text,
        animationDuration: 500,
      },
    },
    {
      nodeStatus: node3Completed ? 'completed' : 'pending',
      lineStatus: 'pending',
      node: { animationDuration: 200 },
    },
  ]
})

// --- StepLine 逻辑结束 ---

watch(() => keyboardFocus.value, (val) => {
  if (val) {
    scrollPageToMiddle('plateNumber')
  }
})

// watch(() => plateNumber.value, (newValue) => {
//   if (newValue?.length >= 7 && keyboardFocus.value) {
//     scrollPageToMiddle('identityCard')
//   }
// })

watch(() => realNameFocus.value, () => {
  scrollPageToMiddle('realName')
})

function trackClick(button) {
  track.click({
    page: TRACK_PAGE,
    button,
  })
}

function handleBack() {
  trackClick(TrackClickButton.BACK)

  if (!info.value?.licensePlateNumber && plateNumber.value?.length < 7 && !isBack.value) {
    showDialogLeave()
    isBack.value = true
    return false
  }
  // if ((!info.value?.idCardFrontUrl || !info.value?.idCardBackUrl) && (!identityCards.value?.front || !identityCards.value?.back) && !isBack.value) {
  //   showDialogForm('identityCard')
  //   isBack.value = true
  //   return false
  // }

  return true
}

function showDialogLeave() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogAmountIntercept, {
        onAction: context.callback,
      })
    },
  }).then((res) => {
    if (res.action === 'confirm') {
      getInfo()
    }
  })
}

// function showDialogForm(itemType) {
//   return dialog.open({
//     render(context) {
//       return h(DialogForm, {
//         itemType,
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'confirm') {
//       getInfo()
//     }
//   })
// }

// function uploadTwice() {
//   if (identityCards.value.front && identityCards.value.back) {
//     trackClick(TrackClickButton.FINISH_ID)
//   }
// }

async function scrollPageToMiddle(type: string) {
  const formEl: HTMLElement | null = document.querySelector('#form')
  if (!formEl) {
    return
  }
  const formTop = formEl.offsetTop

  let scrollTop: number

  if (type === 'plateNumber') {
    scrollTop = formTop + (isIOS ? 280 : 260)
  }
  else {
    scrollTop = formTop
  }

  setTimeout(() => {
    window.scrollTo({
      top: scrollTop,
      behavior: 'smooth',
    })
  }, 100)
}

// function navBackIconClick() {
//   const shouldBack = handleBack()

//   if (shouldBack) {
//     window.history.back()
//   }
// }

// function showDialogLoginUpdate() {
//   return dialog.open({
//     render(context) {
//       return h(DialogLoginUpdate, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'success') {
//       window.location.reload()
//     }
//     return Promise.reject(res)
//   })
// }

function toggleProtocol() {
  readProtocol.value = !readProtocol.value
  if (readProtocol.value) {
    agreementCheckTime.value = Date.now()
  }
}

function onNext() {
  realNameRef.value?.blur()

  setTimeout(() => {
    plateNumberRef.value?.handleShowKeyboard()
  }, 400)
}

onMounted(() => {
  setTimeout(() => {
    // isMounted.value = true // This line is removed as per the new logic
  }, 200)

  setTimeout(() => {
    if (realNameRef.value && !realName.value) {
      realNameRef.value.focus()
      realNameFocus.value = true
    }
    else {
      plateNumberRef.value?.handleShowKeyboard()
    }
  }, 1000)

  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })

  // 属于联登录需要设置返回到指定页面
  if (unionLogin.isUnionLogin.value) {
    pageBackInterceptor.setBackHandler()
  }

  pageBackInterceptor.add(handleBack)
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})
</script>

<template>
  <CarStepLayout class="car-staging-stap-layout" :step-bg="stepBg" record-margin-top="100px" content-padding="0">
    <template #header>
      <MyNavBar title="额度评估中" @back="goBack()" />
    </template>

    <div class="apply-loan-container">
      <div class="content-wrapper">
        <div class="step-line">
          <StepLine :steps="stepsConfig" :animation-type="animationType" :line-heights="[100, 120]" />
        </div>
        <form id="form" class="form" @submit.prevent>
          <div class="item">
            <div class="title-box">
              <div class="left">
                <img src="@/assets/images/p2025072201/user.png" alt="user" class="user-icon">
                <span>真实姓名</span>
              </div>
              <div v-show="realName" class="right">
                <span>成功率提升20%</span>
                <img src="@/assets/images/p2025072201/up.png" alt="up" class="up-icon">
              </div>
            </div>
            <div class="content">
              <div class="input-box">
                <span>姓名</span>
                <input
                  ref="realNameRef"
                  v-model.trim="realName"
                  class="input"
                  type="text"
                  placeholder="请输入您的姓名"
                  maxlength="11"
                  enterkeyhint="next"
                  @input="($event: any) => realName = $event.target.value.replace(/[^a-zA-Z\u4e00-\u9fa5]/g, '')"
                  @focus="() => { trackClick(TrackClickButton.NAME);realNameFocus = true; }"
                  @blur="() => { realNameFocus = false;realName && trackClick(TrackClickButton.FINISH_NAME) }"
                  @keyup.enter="onNext"
                >
              </div>
            </div>
          </div>
          <div class="item">
            <div class="title-box">
              <div class="left">
                <img src="@/assets/images/p2025072201/car.png" alt="car" class="car-icon">
                <span>车辆状态</span>
              </div>
              <div class="right">
                <span>成功率提升20%</span>
                <img src="@/assets/images/p2025072201/up.png" alt="up" class="up-icon">
              </div>
            </div>
            <div class="content">
              <CarStatus v-model="carStatus" @change="trackClick(TrackClickButton.CAR_STATUS)" />
            </div>
          </div>

          <div class="car-item">
            <PlateNumber
              id="plate-number" ref="plateNumberRef" v-model="plateNumber" class="plate"
              @invoke="trackClick(TrackClickButton.CAR)" @done="trackClick(TrackClickButton.FINISH)"
              @focus="keyboardFocus = true" @blur="keyboardFocus = false"
            />
          </div>

          <div v-show="false">
            <RegionPicker v-model="region" @invoke="trackClick(TrackClickButton.CITY)">
              <template #default="{ handleSelect }">
                <div class="row location" @click="handleSelect">
                  <div class="label">
                    您当前所在地
                  </div>
                  <div class="value-row">
                    <div class="value">
                      {{ region.cityName || '请选择' }}
                    </div>
                    <div class="arrow" />
                  </div>
                </div>
              </template>
            </RegionPicker>
          </div>
          <footer class="footer-custom">
            重庆市渝中区科融小额贷款有限责任公司<br>
            渝ICP备18004509号<br>
            客服电话:**********<br>
          </footer>
        </form>
      </div>
    </div>

    <div class="footer" :class="{ 'footer-fixed': keyboardFocus }">
      <!-- bounce -->
      <button class="submit-btn bounce" @click="applyLoan(), trackClick(TrackClickButton.GET_QUOTA)">
        <img class="tips" src="@/assets/images/p2025072201/tips.png" alt="tips">
      </button>
      <div v-if="status.isReverse === 1" class="protocol" @click="toggleProtocol">
        <IconCheckWhite class="icon" :checked="readProtocol" />
        <span class="text">
          我已阅读并同意
          <span class="blue" @click.stop="showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')">《授权相关协议》</span>
        </span>
      </div>
    </div>

    <div class="footer-placeholder" :class="{ 'footer-m-top': showKeyboard }" />
    <!-- <div class="footer-placeholder footer-m-top" /> -->
  </CarStepLayout>
</template>

<style scoped lang="less">
.apply-loan-container {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  margin-top: 20px;

  .content-wrapper {
    display: flex;
    gap: 10px;
    // height: calc(100vh - 100px);
    height: calc(100vh - 260px);
    // overflow-y: auto;
    padding: 15px;
    padding-right: 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #f6f6f6 7%, #f6f6f6 100%);
    box-shadow: 0px -1px 6px 0px rgba(2, 40, 124, 0.08);
    border-radius: 16px 16px 0px 0px;
    border: 1px solid #ffffff;
  }

  .step-line {
    margin-top: 50px;
    width: 18px;
    height: 300px;
  }

  .form {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;

    .item {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      gap: 10px;
      background-color: #fff;
      border-radius: 16px;
      padding: 10px;
      width: 318px;

      .title-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
          display: flex;
          align-items: center;
          gap: 6px;

          img {
            width: 34px;
            height: 34px;
          }

          span {
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
          }
        }

        .right {
          display: flex;
          padding: 4px 4px 4px 10px;
          margin-right: -10px;
          background-color: #1677ff;
          border-radius: 20px 0 0 20px;

          span {
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }

          img {
            width: 17px;
            height: 17px;
          }
        }
      }

      .content {
        .input-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 18px;
          padding: 0 15px;
          height: 40px;
          background: #f4f4f4;
          border-radius: 4px;
          span {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
          }
          .input {
            position: relative;
            height: 100%;
            line-height: 40px;
            background: #f4f4f4;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            text-align: right;

            &::placeholder {
              color: #ccc;
            }
          }
        }
      }
    }

    .car-item {
      background-image: url('@/assets/images/p2025072201/car-no.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      border-radius: 16px;
      padding: 72px 10px 0 0;
      width: 326px;
      height: 130px;
      flex-shrink: 0;
    }
  }

  :deep(.plate-input-wrapper .plate-input .item) {
    margin: 0 1px;
  }
}

.footer-custom {
  margin-top: 30px;
  padding: 15px 0;
  text-align: center;
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

.footer {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px -1px 5px 0px rgba(47, 121, 255, 0.1);
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 19;
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom);
  transition: bottom 0.3s;

  // &.footer-fixed {
  //   bottom: 270px;
  // }

  .submit-btn {
    display: block;
    width: 315px;
    height: 62px;
    margin: 20px auto 0;
    position: relative;
    background-image: url('@/assets/images/p2025072201/btn-2.png');
    background-size: 100% 100%;

    .tips {
      position: absolute;
      top: -15px;
      right: 0px;
      width: 132px;
      height: 22px;
    }
  }

  .protocol {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    margin: 0 auto 10px;

    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;

      &.active {
        color: #1677ff;
      }
    }

    .text {
      vertical-align: middle;

      .blue {
        color: #1677ff;
      }
    }
  }
}

.footer-placeholder {
  height: calc(81px + env(safe-area-inset-bottom));
  height: calc(81px + constant(safe-area-inset-bottom));

  &.footer-m-top {
    margin-top: 100px;
  }
}
</style>
