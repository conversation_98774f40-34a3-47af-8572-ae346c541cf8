import track from '@/utils/track'
import { getTrackInfo } from '@/api/home'
import { useCountdownApp } from '@/composables/useCountdownApp'
import { isLogin } from '@/utils/auth'
import queue from '@/utils/single-queue'
/**
 * 留资节点 (0:未登录 1:手机号 2:车牌信息 3:身份证信息 4:留资完成),
private Integer userInfoNode;
 */
enum TrackUserInfoNode {
  UNLOGIN = 0,
  PHONE = 1,
  CAR = 2,
  ID = 3,
  FINISH = 4,
}

/**
 * 贷款记录节点 (1:未创建；2:已创建；3:预审；4:电核；5:授信；6:放款)
private Integer loadNode;
 */
enum TrackLoadNode {
  UNLOGIN = 0,
  UNCREATE = 1,
  CREATE = 2,
  PRE = 3,
  ELEC = 4,
  CREDIT = 5,
  LOAN = 6,
}

/**
 * 贷款记录状态 (1:已通过;2:未通过)
private Integer loanStatus;
 */
enum TrackLoanStatus {
  UNLOGIN = 0,
  PASS = 1,
  UNPASS = 2,
}

export interface TrackExtendJson {
  userInfoNode: TrackUserInfoNode
  loadNode: TrackLoadNode
  loanStatus: TrackLoanStatus
  childProduceType?: number
  cityId?: number
  pageType?: number
  formFields?: Record<string, any>
}

interface TrackShowParams {
  page: string
}

interface TrackClickParams {
  page?: string
  button: number
  extendJson?: Record<string, unknown>
}

interface StayFunction {
  (params: TrackShowParams & { timerValue?: number }): any
  stop?: () => void
}

interface UseTrackOptions {
  page: string
  autoShow?: boolean
  autoStay?: boolean
  extendJson?: TrackExtendJson
}

export const InitialExtendJson = {
  userInfoNode: 0,
  loadNode: 0,
  loanStatus: 0,
  childProduceType: null,
  cityId: null,
  pageType: 1, // 页面类型 1 落地页 2：app
}

export function useTrack(options: UseTrackOptions) {
  const { autoShow, autoStay, extendJson } = options as UseTrackOptions
  let { page } = options as UseTrackOptions
  const appendExtendJson = extendJson ? { ...InitialExtendJson, ...extendJson } : InitialExtendJson
  const info = ref({
    ...appendExtendJson,
  } as TrackExtendJson)
  let taskId = ''
  function refresh() {
    if (!isLogin()) {
      return Promise.resolve()
    }

    return getTrackInfo().then((res: any) => {
      if (res.code === 200 && res.data) {
        info.value = {
          ...appendExtendJson,
          userInfoNode: res.data.userInfoNode || 0,
          loadNode: res.data.loadNode || 0,
          loanStatus: res.data.loanStatus || 0,
          childProduceType: res.data.childProduceType ?? null,
          cityId: res.data.cityId ?? null,
        }
      }
    })
  }

  function show(params: TrackShowParams) {
    try {
      return track.show({
        page,
        extendJson: info.value,
        extendParam1: 2,
        ...params,
      })
    }
    catch (error) {
      return Promise.resolve(error)
    }
  }

  // 添加倒计时功能的 stay 函数 timerValue 默认2秒发送一次埋点
  const stay: StayFunction = function (params: TrackShowParams & { timerValue?: number }) {
    try {
      const { timerValue = 2 } = params
      // 如果提供了 initialValue，使用倒计时
      const { start: startCountdown, stop } = useCountdownApp({
        initialValue: timerValue,
        onComplete: () => {
          track.stay({
            page,
            extendJson: info.value,
            // extendParam1: 2,
            ...params,
          })
          // 重新开始倒计时，实现循环
          startCountdown()
        },
      })
      stay.stop = stop
      // 开始倒计时
      startCountdown()
    }
    catch (error) {
      return Promise.resolve(error)
    }
  }
  const queueCallBack = {
    callBack: () => {
      stay.stop?.()
      stay({
        page,
      })
    },
    endBack: () => {
      stay.stop?.()
    },
  }
  function click(params: TrackClickParams) {
    try {
      return track.click({
        page,
        ...params,
        extendJson: { ...info.value, ...params.extendJson },
      })
    }
    catch (error) {
      return Promise.resolve(error)
    }
  }
  function replace(params: UseTrackOptions) {
    page = params.page
    show({
      page,
    })
    queue.replace(taskId, queueCallBack)
  }
  onMounted(() => {
    refresh().finally(() => {
      if (autoShow) {
        show({
          page,
        })
      }

      if (autoStay) {
        taskId = queue.push(queueCallBack)
      }
    })
  })

  onBeforeUnmount(() => {
    if (autoStay) {
      queue.nextQueue(taskId)
    }
  })

  return {
    info,
    refresh,
    click,
    show,
    stay,
    replace,
  }
}
