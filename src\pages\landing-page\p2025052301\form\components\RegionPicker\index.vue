<script setup lang="ts">
import ArrowBottom from '@/components/Icons/ArrowBottom.vue'

import { getAllCity, getCityIp } from '@/api/home'
import { ref } from 'vue'
import { PROVINCE_ADCODE_EXCLUDE } from '@/constants'
import bus from '@/utils/bus'
import { debounce } from 'lodash-es'

const props = defineProps({
  modelValue: {
    type: Object,
  },
})

const emit = defineEmits(['update:modelValue', 'invoke', 'done'])

const show = ref(false)
const cascaderValue = ref('')
const options = ref([])

watch(
  () => props.modelValue,
  debounce(async (val) => {
    if (!options.value.length) {
      await getCityList()
    }
    if (val?.cityId) {
      const province = options.value.find((item: any) => String(item.value) === String(val.provinceId))
      if (province) {
        bus.emit('regionChange', {
          ...val,
          provinceShortName: province?.short,
        })
      }
    }
  }, 100),
  { immediate: true, deep: true },
)

onMounted(() => {
  Promise.all([
    getRegion(),
  ]).then((res) => {
    let [res1] = res
    if (!res1) {
      res1 = {
        cityId: '110000',
        cityName: '北京市',
        provinceId: 110000,
        provinceName: '北京市',
      }
    }
    if (props.modelValue?.cityId) {
      return
    }
    emit('update:modelValue', res1)
  })
})

function formateCityTree(node: any): any {
  const result = []

  for (let index = 0; index < node.length; index++) {
    const item = node[index]

    if (PROVINCE_ADCODE_EXCLUDE.includes(item.provinceCode)) {
      continue
    }

    const obj: any = {
      text: item.provinceName,
      value: item.provinceCode,
      short: item.provinceShortName,
    }

    if (Array.isArray(item.cityDtoList)) {
      const children = []
      const arr = item.cityDtoList
      for (let i = 0; i < arr.length; i++) {
        const it = arr[i]
        children.push({
          text: it.fullName,
          value: it.id,
        })
      }
      obj.children = children
    }

    result.push(obj)
  }

  return result
}

function getCityList() {
  return getAllCity().then((res: any) => {
    if (res.code === 200) {
      const { data = [] } = res
      options.value = formateCityTree(data)
    }
  })
}

function getRegion() {
  return getCityIp().then((res: any) => {
    if (res.code === 200 && res.data?.provinceCode && res.data?.cityCode) {
      const val = {
        cityId: res.data.cityCode,
        cityName: res.data.city,
        provinceId: res.data.provinceCode,
        provinceName: res.data.province,
      }

      return val
    }
    return null
  }).catch(() => {
    return null
  })
}

function handleSelect() {
  emit('invoke')

  show.value = true
}

function onFinish({ selectedOptions }) {
  show.value = false
  const cityId = selectedOptions[1].value
  const cityName = selectedOptions[1].text
  const provinceId = selectedOptions[0].value
  const provinceName = selectedOptions[0].text
  const value = {
    cityId,
    cityName,
    provinceId,
    provinceName,
  }

  emit('update:modelValue', value)
  emit('done')
}
</script>

<template>
  <div>
    <slot name="default" :handle-select="handleSelect">
      <div class="region">
        <div class="label">
          归属地
        </div>
        <div class="value flex items-center" @click="handleSelect">
          {{ modelValue.cityName || '*' }}
          <ArrowBottom class="arrow" />
        </div>
      </div>
    </slot>
    <van-popup v-model:show="show" round position="bottom" teleport="body">
      <van-cascader
        v-model="cascaderValue" title="请选择所在地区" :options="options" @close="show = false"
        @finish="onFinish"
      />
    </van-popup>
  </div>
</template>

<style lang='less' scoped>
.region {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: -10px;

  .label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
  }

  .value {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    margin-left: auto;

    .arrow {
      width: 8px;
      height: 4px;
      margin-left: 5px;
    }
  }
}
</style>
